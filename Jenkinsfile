pipeline {
    agent any

    parameters {
        string(
            name: 'JIRA_ISSUE_ID',
            defaultValue: '',
            description: 'JIRA Issue ID (e.g., PROJ-123)',
            trim: true
        )
        string(
            name: 'JIRA_SERVER_URL',
            defaultValue: 'https://your-company.atlassian.net',
            description: 'JIRA Server URL',
            trim: true
        )
    }

    environment {
        JIRA_CREDENTIALS_ID = 'jira-api-credentials'
        JIRA_ISSUE_ID_PATTERN = '^[A-Z]+-[0-9]+$'
    }

    stages {
        stage('Validate Parameters') {
            steps {
                script {
                    if (!params.JIRA_ISSUE_ID) {
                        error("JIRA_ISSUE_ID parameter is required. Please provide a valid JIRA issue ID (e.g., PROJ-123)")
                    }
                    if (!params.JIRA_ISSUE_ID.matches(env.JIRA_ISSUE_ID_PATTERN)) {
                        error("Invalid JIRA Issue ID format: '${params.JIRA_ISSUE_ID}'. Expected format: PROJECT-NUMBER (e.g., PROJ-123)")
                    }
                    if (!params.JIRA_SERVER_URL) {
                        error("JIRA_SERVER_URL parameter is required. Please provide a valid JIRA server URL")
                    }
                    echo "✓ JIRA Issue ID: ${params.JIRA_ISSUE_ID}"
                    echo "✓ JIRA Server URL: ${params.JIRA_SERVER_URL}"
                }
            }
        }

        stage('Connect to JIRA') {
            steps {
                script {
                    try {
                        withCredentials([usernamePassword(
                            credentialsId: env.JIRA_CREDENTIALS_ID,
                            usernameVariable: 'JIRA_USERNAME',
                            passwordVariable: 'JIRA_PASSWORD'
                        )]) {
                            echo "Testing connection to JIRA server: ${params.JIRA_SERVER_URL}"

                            def response = sh(
                                script: """
                                    curl -s -w "HTTP_CODE:%{http_code}" \
                                         -u "${JIRA_USERNAME}:${JIRA_PASSWORD}" \
                                         -H "Accept: application/json" \
                                         "${params.JIRA_SERVER_URL}/rest/api/2/serverInfo"
                                """,
                                returnStdout: true
                            ).trim()

                            def httpCode = response.split('HTTP_CODE:')[1]
                            def responseBody = response.split('HTTP_CODE:')[0]

                            if (httpCode != '200') {
                                error("Failed to connect to JIRA server. HTTP Status: ${httpCode}")
                            }

                            echo "✓ Successfully connected to JIRA server"

                            // Parse and display server info
                            def serverInfo = readJSON text: responseBody
                            echo "JIRA Server Version: ${serverInfo.version}"
                            echo "JIRA Server Title: ${serverInfo.serverTitle}"
                        }
                    } catch (Exception e) {
                        error("JIRA connection failed: ${e.getMessage()}")
                    }
                }
            }
        }

        stage('Retrieve JIRA Issue') {
            steps {
                script {
                    try {
                        withCredentials([usernamePassword(
                            credentialsId: env.JIRA_CREDENTIALS_ID,
                            usernameVariable: 'JIRA_USERNAME',
                            passwordVariable: 'JIRA_PASSWORD'
                        )]) {
                            echo "Retrieving JIRA issue: ${params.JIRA_ISSUE_ID}"

                            def response = sh(
                                script: """
                                    curl -s -w "HTTP_CODE:%{http_code}" \
                                         -u "${JIRA_USERNAME}:${JIRA_PASSWORD}" \
                                         -H "Accept: application/json" \
                                         "${params.JIRA_SERVER_URL}/rest/api/2/issue/${params.JIRA_ISSUE_ID}?expand=names,schema,operations,editmeta,changelog,versionedRepresentations"
                                """,
                                returnStdout: true
                            ).trim()

                            def httpCode = response.split('HTTP_CODE:')[1]
                            def responseBody = response.split('HTTP_CODE:')[0]

                            if (httpCode == '404') {
                                error("JIRA issue '${params.JIRA_ISSUE_ID}' not found. Please verify the issue ID exists.")
                            } else if (httpCode == '401') {
                                error("Authentication failed. Please check JIRA credentials.")
                            } else if (httpCode == '403') {
                                error("Access denied. User does not have permission to view issue '${params.JIRA_ISSUE_ID}'.")
                            } else if (httpCode != '200') {
                                error("Failed to retrieve JIRA issue. HTTP Status: ${httpCode}")
                            }

                            echo "✓ Successfully retrieved JIRA issue: ${params.JIRA_ISSUE_ID}"

                            // Store issue data for next stage
                            env.JIRA_ISSUE_DATA = responseBody
                        }
                    } catch (Exception e) {
                        error("Failed to retrieve JIRA issue: ${e.getMessage()}")
                    }
                }
            }
        }